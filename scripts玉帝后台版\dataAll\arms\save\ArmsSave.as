package dataAll.arms.save
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.bookGet.ArmsBookGetter;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.creator.ArmsElementCtrl;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.creator.ArmsUpgradeCtrl;
   import dataAll.arms.creator.GunImageCreator;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsName;
   import dataAll.arms.define.ArmsPro;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.GunPart;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.bullet.BulletBounceDefine;
   import dataAll.bullet.BulletCritDefine;
   import dataAll.bullet.BulletFollowDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ComplexSave;
   import dataAll.items.save.ItemsSave;
   import dataAll.skill.define.SkillDefine;
   import dataAll.things.save.ThingsSaveGroup;
   
   public class ArmsSave extends ComplexSave
   {
      
      public static const ZERO:ArmsSave = new ArmsSave();
      
      private static var partsZero:ThingsSaveGroup = null;
      
      public static var pro_arr:Array = [];
      
      public static var dHaveProArr:Array = null;
      
      public static var dNoProArr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var armsType:String = "";
      
      public var hurtRatio:Number = 0;
      
      public var capacity:int = 0;
      
      public var attackGap:Number = 0;
      
      public var reloadGap:Number = 0;
      
      public var shakeAngle:Number = 0;
      
      public var bulletWidth:int = 7;
      
      public var bulletShakeWidth:int = 0;
      
      public var bulletNum:int = 1;
      
      public var shootAngle:Number = 0;
      
      public var skillArr:Array = [];
      
      public var godSkillArr:Array = [];
      
      public var bounceD:BulletBounceDefine = new BulletBounceDefine();
      
      public var followD:BulletFollowDefine = new BulletFollowDefine();
      
      public var critD:BulletCritDefine = new BulletCritDefine();
      
      public var twoShootPro:Number = 0;
      
      public var penetrationNum:int = 0;
      
      public var penetrationGap:int = 0;
      
      public var shootSoundUrl:String = "";
      
      public var partsSave:ThingsSaveGroup = new ThingsSaveGroup();
      
      public var armsImgLabel:String = "";
      
      public var s:String = "";
      
      public var bh:Number = 0;
      
      public var upgradeB:Boolean = true;
      
      public var strengthenLv:Number = 0;
      
      public var strengthenNum:Number = 0;
      
      public var sMaxLv:Number = 0;
      
      public var evoLv:int = 1;
      
      public var ele:String = "";
      
      public var eleLv:* = 0;
      
      public var firstChoiceB:Boolean = false;
      
      public var l:Array = [];
      
      public var o:Object = {};
      
      public function ArmsSave()
      {
         super();
         itemsType = ItemsDataGroup.TYPE_ARMS;
         this.partsSave.initSaveInArmsParts();
      }
      
      public static function panPartsZero(tg0:ThingsSaveGroup) : Boolean
      {
         if(tg0.arr.length == 0 && ObjectMethod.getObjElementNum(tg0.lockObj) == 0)
         {
            if(partsZero == null)
            {
               partsZero = new ThingsSaveGroup();
               partsZero.initSaveInArmsParts();
            }
            if(tg0.gripMaxNum != partsZero.gripMaxNum)
            {
               return false;
            }
            if(tg0.lockLen != partsZero.lockLen)
            {
               return false;
            }
            return true;
         }
         return false;
      }
      
      public static function getShootSoundUrl(label0:String) : String
      {
         var str0:String = getPartUrl(label0,"barrel");
         if(str0 == "0")
         {
            str0 = "";
         }
         else
         {
            str0 += "_sound";
         }
         return str0;
      }
      
      public static function getPartUrl(label0:String, str0:String) : String
      {
         var index0:int = int(GunPart.ARR.indexOf(str0));
         var arr0:Array = label0.split("_");
         var url0:String = arr0[index0];
         return url0.replace("$","/");
      }
      
      public static function setPartUrl(label0:String, partName0:String, partUrl0:String) : String
      {
         var url0:String = null;
         var new0:String = "";
         var index0:int = int(GunPart.ARR.indexOf(partName0));
         var arr0:Array = label0.split("_");
         for(var i:int = 0; i < arr0.length; i++)
         {
            url0 = arr0[i];
            if(i == index0)
            {
               partUrl0 = partUrl0.replace("$","/");
               url0 = partUrl0;
            }
            if(new0 != "")
            {
               new0 += "_";
            }
            new0 += url0;
         }
         return new0;
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         this.inData_byObjRange(obj0);
         if(Gaming.isLocal())
         {
            this.checkSaveObj2(obj0);
         }
      }
      
      private function inData_byObjRange(obj0:Object) : void
      {
         var d0:ArmsDefine = Gaming.defineGroup.bullet.getArmsDefineInRange(obj0.name);
         // 只有当存档对象中没有对应属性时，才使用默认值
         var pro0:String = null;
         for each(pro0 in dHaveProArr)
         {
            if(!obj0.hasOwnProperty(pro0) && d0.hasOwnProperty(pro0))
            {
               this[pro0] = d0[pro0];
            }
         }
         this.inData_byObjRangeNo(obj0);
      }
      
      private function inData_byObjRangeNo(obj0:Object) : void
      {
         var skillLabel0:String = null;
         var d0:ArmsDefine = Gaming.defineGroup.bullet.getArmsDefineInRange(obj0.name);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.fleshSMaxLv();
         if(this.partsSave.arr.length == 0)
         {
            this.partsSave.initSaveInArmsParts();
         }
         if(obj0.hasOwnProperty("skillLabel"))
         {
            skillLabel0 = obj0["skillLabel"];
            if(Boolean(skillLabel0) && skillLabel0 != "")
            {
               this.skillArr.push(skillLabel0);
            }
         }
         itemsType = ItemsDataGroup.TYPE_ARMS;
         if(name == ArmsName.yearTiger)
         {
            this.penetrationNum = 4;
            if(this.godSkillArr.length <= 1)
            {
               ArrayMethod.addNoRepeatInArr(this.godSkillArr,"yearTigerHurtSuper");
            }
         }
         else if(name == ArmsName.yearSheep)
         {
            this.penetrationNum = 12;
         }
         else if(name == ArmsName.yearDog)
         {
            this.bounceD.floor = 20;
         }
         else if(name == ArmsName.lightCone)
         {
            this.penetrationNum = 5;
            this.shootSoundUrl = "";
            if(this.godSkillArr.length <= 1)
            {
               ArrayMethod.addNoRepeatInArr(this.godSkillArr,"lightConeBlind");
            }
         }
         if(Boolean(d0))
         {
            this.armsType = d0.armsType;
         }
      }
      
      public function getSaveObj(oldObj0:Object) : Object
      {
         var d0:ArmsDefine = this.getArmsRangeDefine().def;
         var obj0:Object = ClassProperty.thinSaveObj(this,d0,dHaveProArr);
         var zeroObj0:Object = ClassProperty.thinSaveObj(this,ZERO,dNoProArr);
         ObjectMethod.fixedObj(obj0,zeroObj0);
         if(panPartsZero(this.partsSave) == false)
         {
            obj0["partsSave"] = ClassProperty.copyObj(this.partsSave);
         }
         obj0.name = name;

         // 确保武器编辑的关键属性能够被保存
         if(this.cnName != "" && this.cnName != d0.cnName)
         {
            obj0.cnName = this.cnName;
         }
         if(this.skillArr.length > 0)
         {
            obj0.skillArr = this.skillArr.concat([]);
         }
         if(this.godSkillArr.length > 0)
         {
            obj0.godSkillArr = this.godSkillArr.concat([]);
         }
         if(this.hurtRatio != d0.hurtRatio)
         {
            obj0.hurtRatio = this.hurtRatio;
         }
         if(this.capacity != d0.capacity)
         {
            obj0.capacity = this.capacity;
         }
         if(this.bulletNum != d0.bulletNum)
         {
            obj0.bulletNum = this.bulletNum;
         }
         if(this.itemsLevel != d0.itemsLevel)
         {
            obj0.itemsLevel = this.itemsLevel;
         }
         if(this.attackGap != d0.attackGap)
         {
            obj0.attackGap = this.attackGap;
         }
         if(this.reloadGap != d0.reloadGap)
         {
            obj0.reloadGap = this.reloadGap;
         }
         if(this.strengthenLv != d0.strengthenLv)
         {
            obj0.strengthenLv = this.strengthenLv;
         }
         if(this.evoLv != d0.evoLv)
         {
            obj0.evoLv = this.evoLv;
         }
         if(this.bulletWidth != d0.bulletWidth)
         {
            obj0.bulletWidth = this.bulletWidth;
         }
         if(this.shakeAngle != d0.shakeAngle)
         {
            obj0.shakeAngle = this.shakeAngle;
         }
         if(this.twoShootPro != d0.twoShootPro)
         {
            obj0.twoShootPro = this.twoShootPro;
         }
         if(this.penetrationNum != d0.penetrationNum)
         {
            obj0.penetrationNum = this.penetrationNum;
         }
         if(this.penetrationGap != d0.penetrationGap)
         {
            obj0.penetrationGap = this.penetrationGap;
         }
         if(this.critD.mul != d0.critD.mul)
         {
            obj0.critD = ClassProperty.copyObj(this.critD);
         }
         if(this.bounceD.body != d0.bounceD.body || this.bounceD.floor != d0.bounceD.floor)
         {
            obj0.bounceD = ClassProperty.copyObj(this.bounceD);
         }
         if(this.armsImgLabel != d0.armsImgLabel)
         {
            obj0.armsImgLabel = this.armsImgLabel;
         }
         if(this.shootSoundUrl != d0.shootSoundUrl)
         {
            obj0.shootSoundUrl = this.shootSoundUrl;
         }
         if(this.color != d0.color)
         {
            obj0.color = this.color;
         }
         if(this.armsType != d0.armsType)
         {
            obj0.armsType = this.armsType;
         }

         if(Gaming.isLocal())
         {
            this.checkSaveObj(oldObj0,obj0);
         }
         return obj0;
      }
      
      private function checkSaveObj(oldObj0:Object, obj0:Object) : Boolean
      {
         var s0:ArmsSave = new ArmsSave();
         s0.inData_byObjRange(obj0);
         var a:Object = ClassProperty.copyObj(s0);
         var b:Object = ClassProperty.copyObj(oldObj0);
         var bb0:Boolean = ObjectMethod.samePan(a,b);
         if(bb0 == false)
         {
            INIT.showError(name + "：检测异常不同");
         }
         return bb0;
      }
      
      private function checkSaveObj2(obj0:Object) : Boolean
      {
         var as0:ArmsSave = new ArmsSave();
         as0.inData_byObjRangeNo(obj0);
         var a:Object = ClassProperty.copyObj(as0);
         var b:Object = ClassProperty.copyObj(this);
         var bb0:Boolean = ObjectMethod.samePan(a,b);
         if(bb0 == false)
         {
            INIT.showError(name + "：检测异常不同");
         }
         return bb0;
      }
      
      public function getArmsRangeDefine() : ArmsRangeDefine
      {
         return Gaming.defineGroup.bullet.getArmsRangeDefine(name);
      }
      
      public function getArmsDefine() : ArmsDefine
      {
         var ad0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(name);
         if(Boolean(ad0))
         {
            return ad0.def;
         }
         return null;
      }
      
      public function fleshSMaxLv() : void
      {
         if(this.sMaxLv < this.strengthenLv)
         {
            this.sMaxLv = this.strengthenLv;
         }
      }
      
      override public function isImportantB() : Boolean
      {
         if(super.isImportantB())
         {
            return true;
         }
         if(EquipColor.moreColorPan(color,EquipColor.BLACK))
         {
            if(this.getArmsRangeDefine().def.isRandomB() == false)
            {
               return true;
            }
         }
         return false;
      }
      
      override public function getCnName() : String
      {
         return ArmsEvoCtrl.getCnName(cnName,this.evoLv,this.getArmsRangeDefine().def);
      }
      
      public function getArmsType() : String
      {
         var d0:ArmsDefine = null;
         if(this.armsType == "")
         {
            d0 = this.getArmsDefine();
            if(Boolean(d0))
            {
               this.armsType = d0.armsType;
            }
         }
         return this.armsType;
      }
      
      override public function getChildType() : String
      {
         return this.getArmsType();
      }
      
      override public function getChildTypeCnName() : String
      {
         var d0:ArmsChargerDefine = Gaming.defineGroup.armsCharger.getDefine(this.getArmsType());
         return d0.cnName;
      }
      
      override public function isOnceGetB() : Boolean
      {
         if(Boolean(ArmsBookGetter.getDefine(name)))
         {
            return true;
         }
         return false;
      }
      
      public function haveSpecialB() : Boolean
      {
         return this.penetrationGap > 0 || this.penetrationNum > 0 || this.bounceD.floor > 0 || this.bounceD.body > 0 || this.critD.mul > 0 || this.twoShootPro > 0;
      }
      
      override public function isMoreRedB() : Boolean
      {
         return this.getArmsRangeDefine().def.isMoreRedB();
      }
      
      public function getProValue(pro0:String) : *
      {
         if(pro0 == ArmsPro.shootSpeed)
         {
            return this.getShootSpeed();
         }
         if(pro0 == ArmsPro.precision)
         {
            return this.getOnlyAnglePrecision();
         }
         if(pro0 == ArmsPro.shootWidth)
         {
            return this.getShowShootRange();
         }
         if(this.hasOwnProperty(pro0))
         {
            return this[pro0];
         }
         return this.getArmsRangeDefine().def[pro0];
      }
      
      public function getShootSpeed() : Number
      {
         return 1 / this.attackGap;
      }
      
      public function getOnlyAnglePrecision() : Number
      {
         return ArmsDataCreator.getOnlyAnglePrecision(this.shakeAngle,this.shootAngle);
      }
      
      public function getShowShootRange() : Number
      {
         var d0:ArmsDefine = this.getArmsRangeDefine().def;
         var v0:Number = ArmsDataCreator.getShootRange(d0.armsType,d0.hitType,this.bulletWidth,d0.bulletLife,d0.bulletSpeed,d0.aiShootRange);
         return ArmsDataCreator.getShowShootRange(v0);
      }
      
      public function fleshCnByArmsImgLabel() : void
      {
         cnName = Gaming.defineGroup.bullet.armsName.getArmsCnName(this.getThisPartUrl("barrel"),this.getThisPartUrl("texture"));
      }
      
      public function resetTextureByGodSkill() : void
      {
         var skill0:String = null;
         var skillD0:SkillDefine = null;
         var texture0:String = null;
         if(this.armsImgLabel != "")
         {
            if(this.godSkillArr.length > 0)
            {
               skill0 = this.godSkillArr[0];
               skillD0 = Gaming.defineGroup.skill.getDefine(skill0);
               if(Boolean(skillD0))
               {
                  if(skillD0.secString != "" && skillD0.secString.indexOf("/") > 0)
                  {
                     texture0 = skillD0.secString;
                     this.setThisPartUrl("texture",texture0);
                     this.fleshCnByArmsImgLabel();
                  }
               }
            }
         }
      }
      
      public function setArmsImgLabel(str0:String, defineSoundUrl0:String = "") : void
      {
         this.armsImgLabel = str0;
         this.shootSoundUrl = getShootSoundUrl(this.armsImgLabel);
         if(this.shootSoundUrl == "")
         {
            this.shootSoundUrl = defineSoundUrl0;
         }
      }
      
      public function getImgLabel() : String
      {
         var d0:ArmsSkinDefine = null;
         if(this.s != "")
         {
            d0 = Gaming.defineGroup.armsCharger.getSkin(this.s);
            if(Boolean(d0))
            {
               return GunImageCreator.getArmsImgLabelByUrl(d0.getUrl());
            }
            this.s = "";
         }
         return this.armsImgLabel;
      }
      
      public function getNowSkin() : String
      {
         if(this.s == "")
         {
            return this.getBaseSkin();
         }
         return this.s;
      }
      
      public function getBaseSkin() : String
      {
         return GunImageCreator.getSkinByArmsImgLabel(this.armsImgLabel);
      }
      
      public function getThisPartUrl(partName0:String) : String
      {
         return getPartUrl(this.getImgLabel(),partName0);
      }
      
      public function setThisPartUrl(partName0:String, partUrl0:String) : void
      {
         this.armsImgLabel = setPartUrl(this.armsImgLabel,partName0,partUrl0);
      }
      
      override public function getTrueLevel() : int
      {
         return itemsLevel + addLevel;
      }
      
      public function getMaxLevel() : int
      {
         var add0:int = ArmsUpgradeCtrl.maxAddLevel;
         return int(itemsLevel + add0);
      }
      
      override public function getStrengthenLv() : int
      {
         return this.strengthenLv;
      }
      
      override public function getEvoLv() : int
      {
         return this.evoLv;
      }
      
      override public function setStrengthenLvAndMax(lv0:int) : void
      {
         this.strengthenLv = lv0;
         this.sMaxLv = lv0;
         lockB = true;
      }
      
      public function getStarNum() : int
      {
         return int(this.strengthenLv / 5);
      }
      
      public function doEvo(addLv0:int = 1) : void
      {
         var i:int = 0;
         var skillName0:String = null;
         var skillArr0:Array = ArmsEvoCtrl.darkgoldSkillArr;
         var d0:ArmsDefine = this.getArmsRangeDefine().def;
         this.evoLv += addLv0;
         var panLv0:int = this.evoLv + d0.evoMustFirstLv;
         if(panLv0 == ArmsEvoCtrl.darkgoldLv)
         {
            color = EquipColor.DARKGOLD;
            for(i = 0; i < this.godSkillArr.length; i++)
            {
               skillName0 = this.godSkillArr[i];
               if(skillArr0.indexOf(skillName0) >= 0)
               {
                  this.godSkillArr[i] = skillName0 + "2";
               }
            }
         }
         else if(panLv0 == ArmsEvoCtrl.purgoldLv)
         {
            color = EquipColor.PURGOLD;
            ArmsEvoCtrl.doEvoPurgold(this);
         }
         else if(panLv0 == ArmsEvoCtrl.yagoldLv)
         {
            color = EquipColor.YAGOLD;
            ArmsEvoCtrl.yagoldEvo(this);
         }
      }
      
      public function getEleHurtMul() : Number
      {
         return ArmsElementCtrl.getHurtMul(this.eleLv);
      }
      
      public function getEleAddDps() : Number
      {
         var v0:Number = NaN;
         if(this.ele != "")
         {
            return this.getEleHurtMul() * 0.3;
         }
         return 0;
      }
      
      public function doElement(name0:String) : Boolean
      {
         var lv0:int = 0;
         if(this.ele != name0)
         {
            this.ele = name0;
            if(this.eleLv < 1)
            {
               this.eleLv = 1;
            }
            lockB = true;
            return true;
         }
         lv0 = this.eleLv;
         if(lv0 < ArmsElementCtrl.getMaxLv())
         {
            lv0++;
            this.eleLv = lv0;
            lockB = true;
            return true;
         }
         return false;
      }
      
      public function copy() : ArmsSave
      {
         var s0:ArmsSave = new ArmsSave();
         s0.inData_byObj(this);
         return s0;
      }
      
      override public function copyOne() : ItemsSave
      {
         return this.copy();
      }
      
      override public function getSimulateData(pd0:NormalPlayerData) : IO_ItemsData
      {
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(this,pd0,false);
         da0.fleshData_byEquip(pd0.getHeroMerge());
         return da0;
      }
   }
}

