package UI.forging.shieldUpgrade
{
   import UI.NormalUICtrl;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.shield.ShieldData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.text.TextField;
   
   public class ShieldUpgradeBoard extends NormalUI
   {
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var skillGrip:ItemsGrid = new ItemsGrid();
      
      public var nowData:ShieldData = null;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var gripTag:Sprite;
      
      private var nameTxt:TextField;
      
      private var infoTxt:TextField;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;

      public var Aa:String;

      public var Bb:int;

      public function ShieldUpgradeBoard()
      {
         ExternalInterface.addCallback("shield_EditCheating",Shield_EditCheating);
         super();
         addChild(this.btn);
         addChild(this.mustBox);
         addChild(this.skillGrip);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.skillGrip);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","gripTag","nameTxt","infoTxt","beforeTxt","nextTxt"];
         super.setImg(img0);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("护盾编辑");
         this.skillGrip.setImgToEquipGrip();
         NormalUICtrl.setTag(this.skillGrip,this.gripTag);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"shield");
         this.showOneShieldDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:ShieldData) : void
      {
         if(visible)
         {
            this.showOneShieldDataAndPan(da0);
         }
      }
      
      private function showOneShieldDataAndPan(da0:ShieldData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的护盾。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneShieldData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneShieldData(da0:ShieldData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var d0:HeroSkillDefine = da0.shieldDefine.getSkillDefine();
         var next_d0:HeroSkillDefine = da0.shieldDefine.getNextSkillDefine();
         if(Boolean(d0))
         {
            this.skillGrip.inData_equip(da0);
            this.skillGrip.setNumText("");
            this.nameTxt.text = d0.cnName;
            this.infoTxt.text = d0.getDescriptionNoActiveCd();
            this.beforeTxt.htmlText = ComMethod.color("当前第" + d0.lv + "级","#FF9900") + "\n" + d0.getNowChangeText();
            if(!next_d0)
            {
               this.mustBox.setShowState(false);
               this.btn.actived = false;
               this.nextTxt.htmlText = ComMethod.color("已进阶至最高等级了","#FF9900");
            }
            else
            {
               this.nextTxt.htmlText = ComMethod.color("进阶后第" + next_d0.lv + "级","#FF9900") + "\n" + next_d0.getNowChangeText();
               must_d0 = da0.getUpradeMust();
               bb0 = this.mustBox.inData(must_d0);
               this.btn.actived = bb0;
            }
         }
         else
         {
            this.showNone();
         }
      }
      
      private function showNone() : void
      {
         this.skillGrip.clearData();
         this.nameTxt.text = "";
         this.infoTxt.text = "";
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            // 直接打开编辑界面，不需要检查进阶条件
            this.afterEdit();
         }
      }
      
      private function afterEdit() : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("等级[00*数量] 强化[01*数量] 名称[02*文本]\n品质[03*英文] 技能等级[04*数量]\n\n品质选项: white, green, blue, purple, orange, red\n","",this.Shield_EditCheating);
      }

      public function Shield_EditCheating(str0:String) : void
      {
         var TextArray:Array = new Array();
         TextArray = str0.split("*",str0.length);
         var name:String = String(TextArray[1]);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];

         if(Boolean(this.nowData))
         {
            if(this.Aa == "00" || this.Aa == "等级")
            {
               this.nowData.save.level = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前护盾等级" + TextArray[1]);
            }
            if(this.Aa == "01" || this.Aa == "强化")
            {
               this.nowData.save.strengthenLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前护盾强化等级" + TextArray[1]);
            }
            if(this.Aa == "02" || this.Aa == "名称")
            {
               this.nowData.save.playerName = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前护盾名称" + TextArray[1]);
            }
            if(this.Aa == "03" || this.Aa == "品质")
            {
               this.nowData.save.color = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前护盾品质:" + TextArray[1]);
            }
            if(this.Aa == "04" || this.Aa == "技能等级")
            {
               this.nowData.save.skillLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前护盾技能等级" + TextArray[1]);
            }
         }
      }
   }
}

